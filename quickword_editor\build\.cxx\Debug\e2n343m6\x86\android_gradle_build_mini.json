{"buildFiles": ["C:\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\scripts\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\1PROJECTSGOD\\1QUICKWORD\\quickword_editor\\build\\.cxx\\Debug\\e2n343m6\\x86", "clean"]], "buildTargetsCommandComponents": ["C:\\Users\\<USER>\\AppData\\Local\\Android\\Sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\OneDrive\\Desktop\\1PROJECTSGOD\\1QUICKWORD\\quickword_editor\\build\\.cxx\\Debug\\e2n343m6\\x86", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}