import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../models/formatting_option.dart';

class EditorProvider with ChangeNotifier {
  bool _isSecBarVisible = false;
  List<FormattingOption> _recentOptions = [];
  TextDirection _textDirection = TextDirection.ltr;
  String _currentFont = 'Arial';
  double _currentFontSize = 16.0;
  String _currentAlignment = 'left';
  String _documentContent = '';
  
  // Getters
  bool get isSecBarVisible => _isSecBarVisible;
  List<FormattingOption> get recentOptions => _recentOptions;
  TextDirection get textDirection => _textDirection;
  String get currentFont => _currentFont;
  double get currentFontSize => _currentFontSize;
  String get currentAlignment => _currentAlignment;
  String get documentContent => _documentContent;

  EditorProvider() {
    _loadRecentOptions();
  }

  void toggleSecBar() {
    _isSecBarVisible = !_isSecBarVisible;
    notifyListeners();
  }

  void hideSecBar() {
    _isSecBarVisible = false;
    notifyListeners();
  }

  void toggleTextDirection() {
    _textDirection = _textDirection == TextDirection.ltr 
        ? TextDirection.rtl 
        : TextDirection.ltr;
    notifyListeners();
  }

  void setTextDirection(TextDirection direction) {
    _textDirection = direction;
    notifyListeners();
  }

  void updateDocumentContent(String content) {
    _documentContent = content;
    notifyListeners();
  }

  // Text formatting properties
  bool _isBold = false;
  bool _isItalic = false;
  bool _isUnderline = false;

  bool get isBold => _isBold;
  bool get isItalic => _isItalic;
  bool get isUnderline => _isUnderline;

  void toggleBold() {
    _isBold = !_isBold;
    print('Bold toggled to: $_isBold');
    notifyListeners();
  }

  void toggleItalic() {
    _isItalic = !_isItalic;
    print('Italic toggled to: $_isItalic');
    notifyListeners();
  }

  void toggleUnderline() {
    _isUnderline = !_isUnderline;
    print('Underline toggled to: $_isUnderline');
    notifyListeners();
  }

  void applyFormatting(FormattingOption option) {
    // Apply the formatting based on type
    switch (option.type) {
      case 'font':
        _currentFont = option.value;
        break;
      case 'size':
        _currentFontSize = option.value;
        break;
      case 'alignment':
        _currentAlignment = option.value;
        break;
    }

    // Update usage tracking
    _updateOptionUsage(option);
    notifyListeners();
  }

  void addNewFormattingOption(String type, dynamic value, String displayText) {
    final option = FormattingOption(
      id: '${type}_${value}_${DateTime.now().millisecondsSinceEpoch}',
      displayText: displayText,
      type: type,
      value: value,
      lastUsed: DateTime.now(),
    );

    _updateOptionUsage(option);
  }

  void _updateOptionUsage(FormattingOption option) {
    // Remove existing option if it exists
    _recentOptions.removeWhere((opt) => 
        opt.type == option.type && opt.value == option.value);

    // Add updated option at the beginning
    _recentOptions.insert(0, option.copyWith(
      lastUsed: DateTime.now(),
      usageCount: option.usageCount + 1,
    ));

    // Keep only the 10 most recent options
    if (_recentOptions.length > 10) {
      _recentOptions = _recentOptions.take(10).toList();
    }

    _saveRecentOptions();
  }

  Future<void> _loadRecentOptions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final optionsJson = prefs.getString('recent_options');
      
      if (optionsJson != null) {
        final List<dynamic> optionsList = json.decode(optionsJson);
        _recentOptions = optionsList
            .map((json) => FormattingOption.fromJson(json))
            .toList();
      } else {
        // Initialize with default options
        _initializeDefaultOptions();
      }
    } catch (e) {
      _initializeDefaultOptions();
    }
    notifyListeners();
  }

  Future<void> _saveRecentOptions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final optionsJson = json.encode(
        _recentOptions.map((option) => option.toJson()).toList()
      );
      await prefs.setString('recent_options', optionsJson);
    } catch (e) {
      // Handle error silently
    }
  }

  void _initializeDefaultOptions() {
    _recentOptions = [
      FormattingOption(
        id: 'font_arial',
        displayText: 'Arial',
        type: 'font',
        value: 'Arial',
        lastUsed: DateTime.now(),
      ),
      FormattingOption(
        id: 'size_16',
        displayText: '16',
        type: 'size',
        value: 16.0,
        lastUsed: DateTime.now(),
      ),
      FormattingOption(
        id: 'align_left',
        displayText: '⬅',
        type: 'alignment',
        value: 'left',
        lastUsed: DateTime.now(),
      ),
    ];
  }

  List<FormattingOption> getOptionsByType(String type) {
    return _recentOptions.where((option) => option.type == type).toList();
  }
}
