import 'dart:io';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';

class SimpleDocumentService {
  // Open text document file
  static Future<String?> openDocument() async {
    try {
      print('Opening file picker...');
      
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['txt'],
        allowMultiple: false,
      );

      if (result != null && result.files.isNotEmpty) {
        final pickedFile = result.files.first;
        print('Picked file: ${pickedFile.name}');

        if (pickedFile.path != null) {
          final file = File(pickedFile.path!);
          print('Reading file: ${file.path}');
          
          final content = await file.readAsString();
          print('File read successfully: ${content.length} characters');
          return content;
        } else if (pickedFile.bytes != null) {
          // Handle file from bytes (web platform)
          print('Reading file from bytes...');
          return String.fromCharCodes(pickedFile.bytes!);
        }
      }
      
      print('No file selected');
      return null;
    } catch (e) {
      print('Error opening document: $e');
      return null;
    }
  }

  // Save document to Downloads folder
  static Future<bool> saveDocument(String content, {String? fileName}) async {
    try {
      print('Starting save document process...');
      
      // Get Downloads directory
      final directory = await getExternalStorageDirectory();
      if (directory == null) {
        print('Cannot access external storage');
        return false;
      }
      
      final downloadsPath = '${directory.path}/Download';
      final downloadsDir = Directory(downloadsPath);
      if (!await downloadsDir.exists()) {
        await downloadsDir.create(recursive: true);
      }
      
      // Create filename with timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch;
      final defaultFileName = fileName ?? 'quickword_document_$timestamp.txt';
      final filePath = '$downloadsPath/$defaultFileName';
      final file = File(filePath);
      
      print('Saving to: $filePath');
      
      // Write content to file
      await file.writeAsString(content);
      print('File saved successfully: $filePath');
      return true;
    } catch (e) {
      print('Error saving document: $e');
      return false;
    }
  }

  // Save document with file picker (Save As)
  static Future<bool> saveAsDocument(String content) async {
    try {
      print('Starting Save As...');
      
      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Document As',
        fileName: 'my_document.txt',
        type: FileType.custom,
        allowedExtensions: ['txt'],
      );

      if (outputFile != null) {
        final file = File(outputFile);
        print('Saving to: $outputFile');
        
        await file.writeAsString(content);
        print('File saved successfully: $outputFile');
        return true;
      } else {
        print('Save cancelled by user');
        return false;
      }
    } catch (e) {
      print('Error in Save As: $e');
      // Fallback to regular save
      return await saveDocument(content);
    }
  }
}
