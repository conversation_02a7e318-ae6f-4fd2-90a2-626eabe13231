import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/editor_provider.dart';

class TextEditor extends StatefulWidget {
  const TextEditor({super.key});

  @override
  State<TextEditor> createState() => _TextEditorState();
}

class _TextEditorState extends State<TextEditor> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController();
    _focusNode = FocusNode();

    // Load initial content
    WidgetsBinding.instance.addPostFrameCallback((_) {
      final provider = Provider.of<EditorProvider>(context, listen: false);
      _controller.text = provider.documentContent;
    });
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();
    final provider = Provider.of<EditorProvider>(context, listen: false);
    if (_controller.text != provider.documentContent) {
      _controller.text = provider.documentContent;
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Consumer<EditorProvider>(
      builder: (context, provider, child) {
        return Expanded(
          child: Container(
            margin: const EdgeInsets.all(16),
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Colors.white,
              border: Border.all(color: Colors.grey[300]!),
              borderRadius: BorderRadius.circular(8),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withValues(alpha: 0.1),
                  blurRadius: 4,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Column(
              children: [
                // Document info bar
                Container(
                  padding: const EdgeInsets.symmetric(vertical: 8, horizontal: 12),
                  decoration: BoxDecoration(
                    color: Colors.grey[50],
                    borderRadius: BorderRadius.circular(4),
                  ),
                  child: SingleChildScrollView(
                    scrollDirection: Axis.horizontal,
                    child: Row(
                      children: [
                        Icon(Icons.description, size: 16, color: Colors.grey[600]),
                        const SizedBox(width: 8),
                        Text(
                          'Document - ${provider.textDirection == TextDirection.rtl ? 'RTL' : 'LTR'}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        Text(
                          'Font: ${provider.currentFont} | Size: ${provider.currentFontSize.toInt()} | Align: ${provider.currentAlignment}',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.grey[600],
                          ),
                        ),
                        const SizedBox(width: 16),
                        // Formatting status indicators
                        Row(
                          children: [
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: provider.isBold ? Colors.blue[100] : Colors.grey[200],
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: Text(
                                'B',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontWeight: FontWeight.bold,
                                  color: provider.isBold ? Colors.blue[800] : Colors.grey[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: provider.isItalic ? Colors.blue[100] : Colors.grey[200],
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: Text(
                                'I',
                                style: TextStyle(
                                  fontSize: 10,
                                  fontStyle: FontStyle.italic,
                                  color: provider.isItalic ? Colors.blue[800] : Colors.grey[600],
                                ),
                              ),
                            ),
                            const SizedBox(width: 4),
                            Container(
                              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                              decoration: BoxDecoration(
                                color: provider.isUnderline ? Colors.blue[100] : Colors.grey[200],
                                borderRadius: BorderRadius.circular(3),
                              ),
                              child: Text(
                                'U',
                                style: TextStyle(
                                  fontSize: 10,
                                  decoration: TextDecoration.underline,
                                  color: provider.isUnderline ? Colors.blue[800] : Colors.grey[600],
                                ),
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                // Text editor
                Expanded(
                  child: Directionality(
                    textDirection: provider.textDirection,
                    child: TextField(
                      controller: _controller,
                      focusNode: _focusNode,
                      maxLines: null,
                      expands: true,
                      textAlignVertical: TextAlignVertical.top,
                      textAlign: _getTextAlign(provider.currentAlignment),
                      style: TextStyle(
                        fontFamily: provider.currentFont,
                        fontSize: provider.currentFontSize,
                        height: 1.5,
                        fontWeight: provider.isBold ? FontWeight.bold : FontWeight.normal,
                        fontStyle: provider.isItalic ? FontStyle.italic : FontStyle.normal,
                        decoration: provider.isUnderline ? TextDecoration.underline : TextDecoration.none,
                      ),
                      decoration: const InputDecoration(
                        border: InputBorder.none,
                        hintText: 'Start typing your document...\n\nTip: Use the Q button to access quick formatting options.',
                        hintStyle: TextStyle(color: Colors.grey),
                        contentPadding: EdgeInsets.all(16),
                      ),
                      onChanged: (value) {
                        provider.updateDocumentContent(value);
                      },
                    ),
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  TextAlign _getTextAlign(String alignment) {
    switch (alignment) {
      case 'left':
        return TextAlign.left;
      case 'center':
        return TextAlign.center;
      case 'right':
        return TextAlign.right;
      case 'justify':
        return TextAlign.justify;
      default:
        return TextAlign.left;
    }
  }
}
