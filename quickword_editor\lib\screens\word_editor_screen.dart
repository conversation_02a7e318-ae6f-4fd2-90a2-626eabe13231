import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/editor_provider.dart';
import '../widgets/top_menu_bar.dart';
import '../widgets/secondary_bar.dart';
import '../widgets/text_editor.dart';
import '../services/simple_document_service.dart';

class WordEditorScreen extends StatelessWidget {
  const WordEditorScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.grey[50],
      body: SafeArea(
        child: Column(
          children: [
            // Top Menu Bar with fixed Q button
            const TopMenuBar(),

            // Secondary Bar (SEC BAR) - shows when Q is pressed
            const SecondaryBar(),

            // Main text editor area
            const TextEditor(),

            // Status bar
            _buildStatusBar(context),
          ],
        ),
      ),
      floatingActionButton: _buildFloatingActions(context),
    );
  }

  Widget _buildStatusBar(BuildContext context) {
    return Consumer<EditorProvider>(
      builder: (context, provider, child) {
        return Container(
          height: 30,
          padding: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: Colors.grey[200],
            border: Border(
              top: BorderSide(color: Colors.grey[300]!, width: 1),
            ),
          ),
          child: Row(
            children: [
              Text(
                'Ready',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const Spacer(),
              Text(
                'Supported: TXT files',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
              const SizedBox(width: 16),
              Text(
                'Text Direction: ${provider.textDirection == TextDirection.rtl ? 'RTL' : 'LTR'}',
                style: TextStyle(
                  fontSize: 12,
                  color: Colors.grey[600],
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildFloatingActions(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        // Open document button
        FloatingActionButton(
          heroTag: "open",
          onPressed: () => _openDocument(context),
          backgroundColor: Colors.blue,
          child: const Icon(Icons.folder_open, color: Colors.white),
        ),
        const SizedBox(height: 8),
        
        // Save document button
        FloatingActionButton(
          heroTag: "save",
          onPressed: () => _saveDocument(context),
          backgroundColor: Colors.green,
          child: const Icon(Icons.save, color: Colors.white),
        ),
        const SizedBox(height: 8),
        
        // Toggle text direction button
        Consumer<EditorProvider>(
          builder: (context, provider, child) {
            return FloatingActionButton(
              heroTag: "direction",
              onPressed: () => provider.toggleTextDirection(),
              backgroundColor: Colors.orange,
              child: Icon(
                provider.textDirection == TextDirection.rtl 
                    ? Icons.format_textdirection_r_to_l 
                    : Icons.format_textdirection_l_to_r,
                color: Colors.white,
              ),
            );
          },
        ),
      ],
    );
  }

  Future<void> _openDocument(BuildContext context) async {
    try {
      final content = await SimpleDocumentService.openDocument();
      if (content != null) {
        final provider = Provider.of<EditorProvider>(context, listen: false);
        provider.updateDocumentContent(content);
        
        // Auto-detect text direction based on content
        if (_containsRtlText(content)) {
          provider.setTextDirection(TextDirection.rtl);
        }
        
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Document opened successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error opening document: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  Future<void> _saveDocument(BuildContext context) async {
    try {
      final provider = Provider.of<EditorProvider>(context, listen: false);
      final success = await SimpleDocumentService.saveDocument(
        provider.documentContent,
        fileName: 'quickword_document.docx',
      );
      
      if (success) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Document saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Error saving document: $e'),
          backgroundColor: Colors.red,
        ),
      );
    }
  }

  bool _containsRtlText(String text) {
    // Simple RTL detection - check for Arabic, Hebrew, or other RTL characters
    final rtlPattern = RegExp(r'[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return rtlPattern.hasMatch(text);
  }
}
