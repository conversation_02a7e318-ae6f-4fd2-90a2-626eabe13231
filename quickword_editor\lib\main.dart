import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'providers/editor_provider.dart';
import 'screens/word_editor_screen.dart';

void main() {
  runApp(const QuickWordApp());
}

class QuickWordApp extends StatelessWidget {
  const QuickWordApp({super.key});

  @override
  Widget build(BuildContext context) {
    return ChangeNotifierProvider(
      create: (context) => EditorProvider(),
      child: MaterialApp(
        title: 'QuickWord Editor',
        theme: ThemeData(
          colorScheme: ColorScheme.fromSeed(seedColor: Colors.blue),
          useMaterial3: true,
        ),
        home: const WordEditorScreen(),
        debugShowCheckedModeBanner: false,
      ),
    );
  }
}


