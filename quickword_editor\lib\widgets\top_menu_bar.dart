import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/editor_provider.dart';
import '../services/document_service.dart';

class TopMenuBar extends StatefulWidget {
  const TopMenuBar({super.key});

  @override
  State<TopMenuBar> createState() => _TopMenuBarState();
}

class _TopMenuBarState extends State<TopMenuBar> {

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: Stack(
        children: [
          // Scrollable menu items
          Positioned.fill(
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              padding: const EdgeInsets.symmetric(horizontal: 80), // Space for Q button
              child: Row(
                children: [
                  _buildMenuButton(context, Icons.folder_open, 'Open', () async {
                    await _openDocument(context);
                  }),
                  _buildMenuButton(context, Icons.save, 'Save', () async {
                    await _saveDocument(context);
                  }),
                  _buildMenuButton(context, Icons.format_bold, 'Bold', () {
                    // TODO: Implement bold formatting
                  }),
                  _buildMenuButton(context, Icons.format_italic, 'Italic', () {
                    // TODO: Implement italic formatting
                  }),
                  _buildMenuButton(context, Icons.format_underlined, 'Underline', () {
                    // TODO: Implement underline formatting
                  }),
                  _buildMenuButton(context, Icons.format_color_text, 'Color', () {
                    // TODO: Implement text color
                  }),
                  _buildMenuButton(context, Icons.format_align_left, 'Left', () {
                    final provider = Provider.of<EditorProvider>(context, listen: false);
                    provider.addNewFormattingOption('alignment', 'left', '⬅');
                  }),
                  _buildMenuButton(context, Icons.format_align_center, 'Center', () {
                    final provider = Provider.of<EditorProvider>(context, listen: false);
                    provider.addNewFormattingOption('alignment', 'center', '⬌');
                  }),
                  _buildMenuButton(context, Icons.format_align_right, 'Right', () {
                    final provider = Provider.of<EditorProvider>(context, listen: false);
                    provider.addNewFormattingOption('alignment', 'right', '➡');
                  }),
                  _buildMenuButton(context, Icons.format_list_bulleted, 'Bullets', () {
                    // TODO: Implement bullet list
                  }),
                  _buildMenuButton(context, Icons.format_list_numbered, 'Numbers', () {
                    // TODO: Implement numbered list
                  }),
                  _buildMenuButton(context, Icons.text_fields, 'Font', () {
                    // TODO: Implement font selection
                  }),
                  _buildMenuButton(context, Icons.format_size, 'Size', () {
                    // TODO: Implement font size selection
                  }),
                  _buildMenuButton(context, Icons.swap_horiz, 'RTL/LTR', () {
                    final provider = Provider.of<EditorProvider>(context, listen: false);
                    provider.toggleTextDirection();
                  }),
                ],
              ),
            ),
          ),
          // Fixed Q button in center
          Positioned(
            left: MediaQuery.of(context).size.width / 2 - 25,
            top: 10,
            child: Consumer<EditorProvider>(
              builder: (context, provider, child) {
                return GestureDetector(
                  onTap: () {
                    provider.toggleSecBar();
                  },
                  child: Container(
                    width: 50,
                    height: 40,
                    decoration: BoxDecoration(
                      color: provider.isSecBarVisible ? Colors.blue : Colors.grey[600],
                      shape: BoxShape.circle,
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withValues(alpha: 0.2),
                          blurRadius: 4,
                          offset: const Offset(0, 2),
                        ),
                      ],
                    ),
                    child: const Center(
                      child: Text(
                        'Q',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMenuButton(BuildContext context, IconData icon, String tooltip, VoidCallback onPressed) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(8),
          child: Container(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(icon, size: 20, color: Colors.grey[700]),
                const SizedBox(height: 2),
                Text(
                  tooltip,
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[700],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _openDocument(BuildContext context) async {
    try {
      final content = await DocumentService.openDocument();
      if (content != null && mounted) {
        final provider = Provider.of<EditorProvider>(context, listen: false);
        provider.updateDocumentContent(content);

        // Auto-detect text direction based on content
        if (_containsRtlText(content)) {
          provider.setTextDirection(TextDirection.rtl);
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Document opened successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveDocument(BuildContext context) async {
    try {
      final provider = Provider.of<EditorProvider>(context, listen: false);
      final success = await DocumentService.saveDocument(
        provider.documentContent,
        fileName: 'quickword_document.docx',
      );

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Document saved successfully'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _containsRtlText(String text) {
    // Simple RTL detection - check for Arabic, Hebrew, or other RTL characters
    final rtlPattern = RegExp(r'[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return rtlPattern.hasMatch(text);
  }
}
