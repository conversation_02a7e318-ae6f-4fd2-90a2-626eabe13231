import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/editor_provider.dart';
import '../services/document_service.dart';

class TopMenuBar extends StatefulWidget {
  const TopMenuBar({super.key});

  @override
  State<TopMenuBar> createState() => _TopMenuBarState();
}

class _TopMenuBarState extends State<TopMenuBar> {

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 60,
      decoration: BoxDecoration(
        color: Colors.grey[100],
        border: Border(
          bottom: BorderSide(color: Colors.grey[300]!, width: 1),
        ),
      ),
      child: Stack(
        children: [
          // Left side menu items
          Positioned(
            left: 0,
            top: 0,
            bottom: 0,
            width: MediaQuery.of(context).size.width / 2 - 30, // Half screen minus Q button space
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              child: Row(
                children: [
                  const SizedBox(width: 8),
                  _buildDropdownMenu(context, 'File', [
                    _buildMenuItem('New Document', Icons.description, () => _newDocument()),
                    _buildMenuItem('Open', Icons.folder_open, () => _openDocument(context)),
                    _buildMenuItem('Save', Icons.save, () => _saveDocument(context)),
                    _buildMenuItem('Save As...', Icons.save_as, () => _saveAsDocument(context)),
                    _buildMenuItem('Export', Icons.file_download, () => _exportDocument(context)),
                  ]),
                  _buildDropdownMenu(context, 'Edit', [
                    _buildMenuItem('Undo', Icons.undo, () => _undo()),
                    _buildMenuItem('Redo', Icons.redo, () => _redo()),
                    _buildMenuItem('Cut', Icons.cut, () => _cut()),
                    _buildMenuItem('Copy', Icons.copy, () => _copy()),
                    _buildMenuItem('Paste', Icons.paste, () => _paste()),
                    _buildMenuItem('Select All', Icons.select_all, () => _selectAll()),
                  ]),
                  _buildDropdownMenu(context, 'View', [
                    _buildMenuItem('Zoom In', Icons.zoom_in, () => _zoomIn()),
                    _buildMenuItem('Zoom Out', Icons.zoom_out, () => _zoomOut()),
                    _buildMenuItem('Full Screen', Icons.fullscreen, () => _fullScreen()),
                    _buildMenuItem('Show Ruler', Icons.straighten, () => _toggleRuler()),
                  ]),
                ],
              ),
            ),
          ),
          // Right side menu items
          Positioned(
            right: 0,
            top: 0,
            bottom: 0,
            width: MediaQuery.of(context).size.width / 2 - 30, // Half screen minus Q button space
            child: SingleChildScrollView(
              scrollDirection: Axis.horizontal,
              reverse: true, // Scroll from right to left
              child: Row(
                children: [
                  const SizedBox(width: 8),
                  _buildDropdownMenu(context, 'Tools', [
                    _buildMenuItem('Spell Check', Icons.spellcheck, () {}),
                    _buildMenuItem('Word Count', Icons.text_fields, () {}),
                  ]),
                  _buildDropdownMenu(context, 'Format', [
                    _buildMenuItem('Bold', Icons.format_bold, () => _toggleBold()),
                    _buildMenuItem('Italic', Icons.format_italic, () => _toggleItalic()),
                    _buildMenuItem('Underline', Icons.format_underlined, () => _toggleUnderline()),
                    _buildMenuItem('Font...', Icons.text_fields, () => _showFontDialog(context)),
                    _buildMenuItem('Paragraph...', Icons.format_align_left, () => _showParagraphDialog(context)),
                  ]),
                  _buildDropdownMenu(context, 'Insert', [
                    _buildMenuItem('Table', Icons.table_chart, () => _insertTable()),
                    _buildMenuItem('Image', Icons.image, () => _insertImage()),
                    _buildMenuItem('Link', Icons.link, () => _insertLink()),
                    _buildMenuItem('Page Break', Icons.keyboard_return, () => _insertPageBreak()),
                  ]),
                ],
              ),
            ),
          ),
          // Fixed Q button in center
          Positioned(
            left: MediaQuery.of(context).size.width / 2 - 25,
            top: 10,
            child: Consumer<EditorProvider>(
              builder: (context, provider, child) {
                return Material(
                  color: Colors.transparent,
                  child: InkWell(
                    onTap: () {
                      print('Q button tapped! Current SEC BAR visible: ${provider.isSecBarVisible}');
                      provider.toggleSecBar();
                      print('After toggle, SEC BAR visible: ${provider.isSecBarVisible}');

                      // Show snackbar to confirm it's working
                      ScaffoldMessenger.of(context).showSnackBar(
                        SnackBar(
                          content: Text(provider.isSecBarVisible
                              ? 'SEC BAR opened'
                              : 'SEC BAR closed'),
                          duration: const Duration(seconds: 1),
                        ),
                      );
                    },
                    borderRadius: BorderRadius.circular(25),
                    child: Container(
                      width: 50,
                      height: 40,
                      decoration: BoxDecoration(
                        color: provider.isSecBarVisible ? Colors.blue : Colors.grey[600],
                        shape: BoxShape.circle,
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.2),
                            blurRadius: 4,
                            offset: const Offset(0, 2),
                          ),
                        ],
                      ),
                      child: const Center(
                        child: Text(
                          'Q',
                          style: TextStyle(
                            color: Colors.white,
                            fontSize: 20,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDropdownMenu(BuildContext context, String title, List<PopupMenuEntry> items) {
    return PopupMenuButton(
      offset: const Offset(0, 40),
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
        child: Text(
          title,
          style: TextStyle(
            fontSize: 14,
            color: Colors.grey[700],
            fontWeight: FontWeight.w500,
          ),
        ),
      ),
      itemBuilder: (context) => items,
    );
  }

  PopupMenuItem _buildMenuItem(String title, IconData icon, VoidCallback onTap) {
    return PopupMenuItem(
      onTap: onTap,
      child: Row(
        children: [
          Icon(icon, size: 18, color: Colors.grey[700]),
          const SizedBox(width: 12),
          Text(title),
        ],
      ),
    );
  }

  Future<void> _openDocument(BuildContext context) async {
    print('_openDocument called');
    try {
      print('Calling DocumentService.openDocument()...');
      final content = await DocumentService.openDocument();
      print('DocumentService returned content: ${content?.substring(0, content.length > 100 ? 100 : content.length)}...');

      if (content != null && mounted) {
        final provider = Provider.of<EditorProvider>(context, listen: false);
        provider.updateDocumentContent(content);
        print('Updated document content in provider');

        // Auto-detect text direction based on content
        if (_containsRtlText(content)) {
          provider.setTextDirection(TextDirection.rtl);
          print('Set text direction to RTL');
        }

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Document opened successfully (${content.length} characters)'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else if (content == null) {
        print('No file selected or content is null');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('No file selected'),
              backgroundColor: Colors.orange,
            ),
          );
        }
      }
    } catch (e) {
      print('Error in _openDocument: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error opening document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  Future<void> _saveDocument(BuildContext context) async {
    print('_saveDocument called');
    try {
      final provider = Provider.of<EditorProvider>(context, listen: false);
      final content = provider.documentContent;
      print('Saving document with ${content.length} characters');

      if (content.isEmpty) {
        print('Document is empty, adding sample content');
        provider.updateDocumentContent('Sample document content created by QuickWord Editor.\n\nThis is a test document.');
      }

      final success = await DocumentService.saveDocument(
        provider.documentContent,
        fileName: 'quickword_document_${DateTime.now().millisecondsSinceEpoch}.txt',
      );

      print('Save operation result: $success');

      if (success && mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Document saved successfully to Downloads folder'),
            backgroundColor: Colors.green,
          ),
        );
      }
    } catch (e) {
      print('Error in _saveDocument: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error saving document: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  bool _containsRtlText(String text) {
    // Simple RTL detection - check for Arabic, Hebrew, or other RTL characters
    final rtlPattern = RegExp(r'[\u0590-\u05FF\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]');
    return rtlPattern.hasMatch(text);
  }

  // File menu actions
  void _newDocument() {
    print('New document clicked');
    final provider = Provider.of<EditorProvider>(context, listen: false);
    provider.updateDocumentContent('');
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('New document created')),
      );
    }
  }

  Future<void> _saveAsDocument(BuildContext context) async {
    await _saveDocument(context);
  }

  Future<void> _exportDocument(BuildContext context) async {
    await _saveDocument(context);
  }

  // Edit menu actions
  void _undo() {
    // TODO: Implement undo functionality
  }

  void _redo() {
    // TODO: Implement redo functionality
  }

  void _cut() {
    // TODO: Implement cut functionality
  }

  void _copy() {
    // TODO: Implement copy functionality
  }

  void _paste() {
    // TODO: Implement paste functionality
  }

  void _selectAll() {
    // TODO: Implement select all functionality
  }

  // View menu actions
  void _zoomIn() {
    // TODO: Implement zoom in functionality
  }

  void _zoomOut() {
    // TODO: Implement zoom out functionality
  }

  void _fullScreen() {
    // TODO: Implement full screen functionality
  }

  void _toggleRuler() {
    // TODO: Implement ruler toggle functionality
  }

  // Insert menu actions
  void _insertTable() {
    // TODO: Implement table insertion
  }

  void _insertImage() {
    // TODO: Implement image insertion
  }

  void _insertLink() {
    // TODO: Implement link insertion
  }

  void _insertPageBreak() {
    // TODO: Implement page break insertion
  }

  // Format menu actions
  void _toggleBold() {
    final provider = Provider.of<EditorProvider>(context, listen: false);
    provider.addNewFormattingOption('style', 'bold', 'B');
  }

  void _toggleItalic() {
    final provider = Provider.of<EditorProvider>(context, listen: false);
    provider.addNewFormattingOption('style', 'italic', 'I');
  }

  void _toggleUnderline() {
    final provider = Provider.of<EditorProvider>(context, listen: false);
    provider.addNewFormattingOption('style', 'underline', 'U');
  }

  void _showFontDialog(BuildContext context) {
    final fonts = ['Arial', 'Times New Roman', 'Helvetica', 'Calibri', 'Georgia', 'Verdana'];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Font'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: ListView.builder(
            itemCount: fonts.length,
            itemBuilder: (context, index) {
              final font = fonts[index];
              return ListTile(
                title: Text(
                  font,
                  style: TextStyle(fontFamily: font),
                ),
                onTap: () {
                  final provider = Provider.of<EditorProvider>(context, listen: false);
                  provider.addNewFormattingOption('font', font, font);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showParagraphDialog(BuildContext context) {
    final alignments = [
      {'value': 'left', 'display': '⬅', 'name': 'Left'},
      {'value': 'center', 'display': '⬌', 'name': 'Center'},
      {'value': 'right', 'display': '➡', 'name': 'Right'},
      {'value': 'justify', 'display': '⬌', 'name': 'Justify'},
    ];

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Paragraph Alignment'),
        content: SizedBox(
          width: 200,
          height: 300,
          child: ListView.builder(
            itemCount: alignments.length,
            itemBuilder: (context, index) {
              final alignment = alignments[index];
              return ListTile(
                leading: Text(
                  alignment['display']!,
                  style: const TextStyle(fontSize: 20),
                ),
                title: Text(alignment['name']!),
                onTap: () {
                  final provider = Provider.of<EditorProvider>(context, listen: false);
                  provider.addNewFormattingOption(
                    'alignment',
                    alignment['value']!,
                    alignment['display']!
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
