import 'dart:io';
import 'dart:typed_data';
import 'package:archive/archive.dart';
import 'package:xml/xml.dart';
import 'package:file_picker/file_picker.dart';
import 'package:path_provider/path_provider.dart';

class DocumentService {
  static const List<String> supportedExtensions = ['doc', 'docx', 'txt', 'rtf'];

  // Open document file
  static Future<String?> openDocument() async {
    try {
      print('Opening file picker...');
      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.any,
        allowMultiple: false,
      );

      print('File picker result: $result');

      if (result != null && result.files.isNotEmpty) {
        final pickedFile = result.files.first;
        print('Picked file: ${pickedFile.name}, path: ${pickedFile.path}');

        if (pickedFile.path != null) {
          final file = File(pickedFile.path!);
          final extension = pickedFile.extension?.toLowerCase() ?? 'txt';
          print('File extension: $extension');

          if (!await file.exists()) {
            throw Exception('File does not exist: ${pickedFile.path}');
          }

          switch (extension) {
            case 'docx':
              print('Reading DOCX file...');
              return await _readDocxFile(file);
            case 'doc':
              print('Reading DOC file...');
              return await _readDocFile(file);
            case 'txt':
            case 'text':
              print('Reading TXT file...');
              return await file.readAsString();
            case 'rtf':
              print('Reading RTF file...');
              return await _readRtfFile(file);
            default:
              print('Unknown extension, treating as text file...');
              return await file.readAsString();
          }
        } else if (pickedFile.bytes != null) {
          // Handle file from bytes if path is not available
          print('Reading file from bytes...');
          return String.fromCharCodes(pickedFile.bytes!);
        } else {
          throw Exception('File path and bytes are both null');
        }
      } else {
        print('No file selected');
        return null;
      }
    } catch (e) {
      print('Error opening document: $e');
      throw Exception('Error opening document: $e');
    }
  }

  // Save document file
  static Future<bool> saveDocument(String content, {String? fileName}) async {
    try {
      print('Starting save document process...');

      // Use file picker to let user choose save location
      String? outputFile = await FilePicker.platform.saveFile(
        dialogTitle: 'Save Document',
        fileName: fileName ?? 'quickword_document.txt',
        type: FileType.custom,
        allowedExtensions: ['txt', 'docx', 'rtf'],
      );

      if (outputFile != null) {
        final file = File(outputFile);
        print('Saving to: $outputFile');

        await file.writeAsString(content);
        print('File saved successfully to: $outputFile');
        return true;
      } else {
        print('Save cancelled by user');
        return false;
      }
    } catch (e) {
      print('Error saving document: $e');
      // Fallback to Downloads folder if file picker fails
      try {
        final directory = await getExternalStorageDirectory();
        if (directory == null) {
          throw Exception('Cannot access external storage');
        }

        final downloadsPath = '${directory.path}/Download';
        final downloadsDir = Directory(downloadsPath);
        if (!await downloadsDir.exists()) {
          await downloadsDir.create(recursive: true);
        }

        final timestamp = DateTime.now().millisecondsSinceEpoch;
        final defaultFileName = fileName ?? 'quickword_document_$timestamp.txt';
        final filePath = '$downloadsPath/$defaultFileName';
        final file = File(filePath);

        print('Fallback: Saving to Downloads: $filePath');

        await file.writeAsString(content);
        print('File saved successfully to Downloads');
        return true;
      } catch (fallbackError) {
        print('Fallback save also failed: $fallbackError');
        throw Exception('Error saving document: $e');
      }
    }
  }

  // Read DOCX file
  static Future<String> _readDocxFile(File file) async {
    try {
      final bytes = await file.readAsBytes();
      final archive = ZipDecoder().decodeBytes(bytes);

      // Find document.xml
      ArchiveFile? documentXml;
      for (final file in archive) {
        if (file.name == 'word/document.xml') {
          documentXml = file;
          break;
        }
      }

      if (documentXml == null) {
        throw Exception('Invalid DOCX file: document.xml not found');
      }

      final xmlContent = String.fromCharCodes(documentXml.content as List<int>);
      final document = XmlDocument.parse(xmlContent);

      // Extract text from XML
      final textNodes = document.findAllElements('w:t');
      final buffer = StringBuffer();

      for (final node in textNodes) {
        buffer.write(node.innerText);
      }

      // Add paragraph breaks
      final paragraphs = document.findAllElements('w:p');
      if (paragraphs.length > 1) {
        return _formatParagraphs(document);
      }

      return buffer.toString();
    } catch (e) {
      throw Exception('Error reading DOCX file: $e');
    }
  }

  // Format paragraphs with proper line breaks
  static String _formatParagraphs(XmlDocument document) {
    final paragraphs = document.findAllElements('w:p');
    final buffer = StringBuffer();

    for (final paragraph in paragraphs) {
      final textNodes = paragraph.findAllElements('w:t');
      for (final node in textNodes) {
        buffer.write(node.innerText);
      }
      buffer.writeln(); // Add line break after each paragraph
    }

    return buffer.toString().trim();
  }

  // Read DOC file (simplified - basic text extraction)
  static Future<String> _readDocFile(File file) async {
    try {
      // DOC files are complex binary format
      // This is a simplified implementation that extracts basic text
      final bytes = await file.readAsBytes();
      
      // Look for text patterns in the binary data
      final text = String.fromCharCodes(bytes.where((byte) => 
        byte >= 32 && byte <= 126 || byte == 10 || byte == 13).toList());
      
      // Clean up the extracted text
      return text.replaceAll(RegExp(r'[^\w\s\n\r\p{L}\p{N}\p{P}\p{S}]', unicode: true), '')
                .replaceAll(RegExp(r'\s+'), ' ')
                .trim();
    } catch (e) {
      throw Exception('Error reading DOC file: $e');
    }
  }

  // Read RTF file
  static Future<String> _readRtfFile(File file) async {
    try {
      final content = await file.readAsString();
      
      // Basic RTF text extraction (remove RTF control codes)
      String text = content.replaceAll(RegExp(r'\\[a-z]+\d*\s?'), '');
      text = text.replaceAll(RegExp(r'[{}]'), '');
      text = text.replaceAll(RegExp(r'\s+'), ' ');
      
      return text.trim();
    } catch (e) {
      throw Exception('Error reading RTF file: $e');
    }
  }

  // Save as DOCX
  static Future<bool> _saveAsDocx(String content, File file) async {
    try {
      // Create a basic DOCX structure
      final archive = Archive();

      // Create [Content_Types].xml
      final contentTypes = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Types xmlns="http://schemas.openxmlformats.org/package/2006/content-types">
  <Default Extension="rels" ContentType="application/vnd.openxmlformats-package.relationships+xml"/>
  <Default Extension="xml" ContentType="application/xml"/>
  <Override PartName="/word/document.xml" ContentType="application/vnd.openxmlformats-officedocument.wordprocessingml.document.main+xml"/>
</Types>''';

      // Create _rels/.rels
      final rels = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<Relationships xmlns="http://schemas.openxmlformats.org/package/2006/relationships">
  <Relationship Id="rId1" Type="http://schemas.openxmlformats.org/officeDocument/2006/relationships/officeDocument" Target="word/document.xml"/>
</Relationships>''';

      // Create word/document.xml with content
      final paragraphs = content.split('\n').map((line) => 
        '<w:p><w:r><w:t>${_escapeXml(line)}</w:t></w:r></w:p>').join('');

      final documentXml = '''<?xml version="1.0" encoding="UTF-8" standalone="yes"?>
<w:document xmlns:w="http://schemas.openxmlformats.org/wordprocessingml/2006/main">
  <w:body>
    $paragraphs
  </w:body>
</w:document>''';

      // Add files to archive
      archive.addFile(ArchiveFile('[Content_Types].xml', contentTypes.length, contentTypes.codeUnits));
      archive.addFile(ArchiveFile('_rels/.rels', rels.length, rels.codeUnits));
      archive.addFile(ArchiveFile('word/document.xml', documentXml.length, documentXml.codeUnits));

      // Encode and save
      final zipData = ZipEncoder().encode(archive);
      await file.writeAsBytes(zipData!);

      return true;
    } catch (e) {
      throw Exception('Error saving DOCX file: $e');
    }
  }

  // Save as RTF
  static Future<bool> _saveAsRtf(String content, File file) async {
    try {
      final rtfContent = '''{\\rtf1\\ansi\\deff0 {\\fonttbl {\\f0 Times New Roman;}}
\\f0\\fs24 ${content.replaceAll('\n', '\\par ')}}''';
      
      await file.writeAsString(rtfContent);
      return true;
    } catch (e) {
      throw Exception('Error saving RTF file: $e');
    }
  }

  // Escape XML special characters
  static String _escapeXml(String text) {
    return text
        .replaceAll('&', '&amp;')
        .replaceAll('<', '&lt;')
        .replaceAll('>', '&gt;')
        .replaceAll('"', '&quot;')
        .replaceAll("'", '&apos;');
  }

  // Get supported file types for display
  static String getSupportedFormatsString() {
    return supportedExtensions.map((ext) => ext.toUpperCase()).join(', ');
  }
}
