import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../providers/editor_provider.dart';
import '../models/formatting_option.dart';

class SecondaryBar extends StatelessWidget {
  const SecondaryBar({super.key});

  @override
  Widget build(BuildContext context) {
    return Consumer<EditorProvider>(
      builder: (context, provider, child) {
        print('SecondaryBar build - isSecBarVisible: ${provider.isSecBarVisible}');
        print('SecondaryBar build - recentOptions count: ${provider.recentOptions.length}');

        if (!provider.isSecBarVisible) {
          return const SizedBox.shrink();
        }

        return Container(
          height: 60,
          decoration: BoxDecoration(
            color: Colors.blue[50],
            border: Border(
              bottom: BorderSide(color: Colors.blue[200]!, width: 1),
            ),
          ),
          child: provider.recentOptions.isEmpty
              ? Center(
                  child: Text(
                    'No recent formatting options. Use Format menu to add some!',
                    style: TextStyle(color: Colors.grey[600], fontSize: 12),
                  ),
                )
              : SingleChildScrollView(
                  scrollDirection: Axis.horizontal,
                  padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                  child: Row(
                    children: [
                      // Add a test button to verify SEC BAR is working
                      _buildTestButton(context, provider),
                      const SizedBox(width: 8),
                      ...provider.recentOptions.map((option) {
                        return _buildOptionCircle(context, option, provider);
                      }).toList(),
                    ],
                  ),
                ),
        );
      },
    );
  }

  Widget _buildTestButton(BuildContext context, EditorProvider provider) {
    return GestureDetector(
      onTap: () {
        print('Test button tapped - adding sample formatting options');
        provider.addNewFormattingOption('font', 'Arial', 'Arial');
        provider.addNewFormattingOption('size', 16.0, '16');
        provider.addNewFormattingOption('alignment', 'left', '⬅');

        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Added sample formatting options to SEC BAR'),
            duration: Duration(seconds: 1),
          ),
        );
      },
      child: Container(
        width: 44,
        height: 44,
        decoration: BoxDecoration(
          color: Colors.green[100],
          shape: BoxShape.circle,
          border: Border.all(color: Colors.green[300]!, width: 2),
        ),
        child: const Center(
          child: Icon(Icons.add, color: Colors.green, size: 20),
        ),
      ),
    );
  }

  Widget _buildOptionCircle(BuildContext context, FormattingOption option, EditorProvider provider) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 4),
      child: GestureDetector(
        onTap: () {
          // Single tap - apply the formatting
          provider.applyFormatting(option);
          provider.hideSecBar();
        },
        onDoubleTap: () {
          // Double tap - open detailed menu for this option type
          _showDetailedMenu(context, option, provider);
        },
        child: Container(
          width: 44,
          height: 44,
          decoration: BoxDecoration(
            color: _getOptionColor(option),
            shape: BoxShape.circle,
            border: Border.all(
              color: Colors.blue[300]!,
              width: 2,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 2,
                offset: const Offset(0, 1),
              ),
            ],
          ),
          child: Center(
            child: _buildOptionContent(option),
          ),
        ),
      ),
    );
  }

  Widget _buildOptionContent(FormattingOption option) {
    switch (option.type) {
      case 'font':
        return Text(
          'Aa',
          style: TextStyle(
            fontFamily: option.value,
            fontSize: 12,
            fontWeight: FontWeight.bold,
            color: Colors.blue[800],
          ),
        );
      case 'size':
        return Text(
          option.displayText,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.blue[800],
          ),
        );
      case 'alignment':
        return Text(
          option.displayText,
          style: TextStyle(
            fontSize: 16,
            color: Colors.blue[800],
          ),
        );
      case 'style':
        return Icon(
          _getStyleIcon(option.value),
          size: 16,
          color: Colors.blue[800],
        );
      default:
        return Text(
          option.displayText,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.blue[800],
          ),
        );
    }
  }

  Color _getOptionColor(FormattingOption option) {
    switch (option.type) {
      case 'font':
        return Colors.green[100]!;
      case 'size':
        return Colors.orange[100]!;
      case 'alignment':
        return Colors.purple[100]!;
      case 'style':
        return Colors.red[100]!;
      default:
        return Colors.blue[100]!;
    }
  }

  IconData _getStyleIcon(String style) {
    switch (style) {
      case 'bold':
        return Icons.format_bold;
      case 'italic':
        return Icons.format_italic;
      case 'underline':
        return Icons.format_underlined;
      default:
        return Icons.text_fields;
    }
  }

  void _showDetailedMenu(BuildContext context, FormattingOption option, EditorProvider provider) {
    provider.hideSecBar();
    
    switch (option.type) {
      case 'font':
        _showFontMenu(context, provider);
        break;
      case 'size':
        _showSizeMenu(context, provider);
        break;
      case 'alignment':
        _showAlignmentMenu(context, provider);
        break;
      case 'style':
        _showStyleMenu(context, provider);
        break;
    }
  }

  void _showFontMenu(BuildContext context, EditorProvider provider) {
    final fonts = ['Arial', 'Times New Roman', 'Helvetica', 'Calibri', 'Georgia', 'Verdana'];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Font'),
        content: SizedBox(
          width: 300,
          height: 400,
          child: ListView.builder(
            itemCount: fonts.length,
            itemBuilder: (context, index) {
              final font = fonts[index];
              return ListTile(
                title: Text(
                  font,
                  style: TextStyle(fontFamily: font),
                ),
                onTap: () {
                  provider.addNewFormattingOption('font', font, font);
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showSizeMenu(BuildContext context, EditorProvider provider) {
    final sizes = [8.0, 10.0, 12.0, 14.0, 16.0, 18.0, 20.0, 24.0, 28.0, 32.0, 36.0, 48.0];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Font Size'),
        content: SizedBox(
          width: 200,
          height: 400,
          child: ListView.builder(
            itemCount: sizes.length,
            itemBuilder: (context, index) {
              final size = sizes[index];
              return ListTile(
                title: Text('${size.toInt()}'),
                onTap: () {
                  provider.addNewFormattingOption('size', size, '${size.toInt()}');
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showAlignmentMenu(BuildContext context, EditorProvider provider) {
    final alignments = [
      {'value': 'left', 'display': '⬅', 'name': 'Left'},
      {'value': 'center', 'display': '⬌', 'name': 'Center'},
      {'value': 'right', 'display': '➡', 'name': 'Right'},
      {'value': 'justify', 'display': '⬌', 'name': 'Justify'},
    ];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Alignment'),
        content: SizedBox(
          width: 200,
          height: 300,
          child: ListView.builder(
            itemCount: alignments.length,
            itemBuilder: (context, index) {
              final alignment = alignments[index];
              return ListTile(
                leading: Text(
                  alignment['display']!,
                  style: const TextStyle(fontSize: 20),
                ),
                title: Text(alignment['name']!),
                onTap: () {
                  provider.addNewFormattingOption(
                    'alignment', 
                    alignment['value']!, 
                    alignment['display']!
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }

  void _showStyleMenu(BuildContext context, EditorProvider provider) {
    final styles = [
      {'value': 'bold', 'icon': Icons.format_bold, 'name': 'Bold'},
      {'value': 'italic', 'icon': Icons.format_italic, 'name': 'Italic'},
      {'value': 'underline', 'icon': Icons.format_underlined, 'name': 'Underline'},
    ];
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Select Style'),
        content: SizedBox(
          width: 200,
          height: 250,
          child: ListView.builder(
            itemCount: styles.length,
            itemBuilder: (context, index) {
              final style = styles[index];
              return ListTile(
                leading: Icon(style['icon'] as IconData),
                title: Text(style['name']! as String),
                onTap: () {
                  provider.addNewFormattingOption(
                    'style',
                    style['value']! as String,
                    style['name']! as String
                  );
                  Navigator.of(context).pop();
                },
              );
            },
          ),
        ),
      ),
    );
  }
}
