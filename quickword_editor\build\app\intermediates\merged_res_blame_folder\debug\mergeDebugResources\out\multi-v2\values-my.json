{"logs": [{"outputFile": "com.example.quickword_editor.app-mergeDebugResources-39:/values-my/values-my.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\66aa7f682cf61ffe3ee75db6ee238d77\\transformed\\core-1.13.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,158,262,365,467,572,678,797", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "153,257,360,462,567,673,792,893"}, "to": {"startLines": "29,30,31,32,33,34,35,41", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2872,2975,3079,3182,3284,3389,3495,4096", "endColumns": "102,103,102,101,104,105,118,100", "endOffsets": "2970,3074,3177,3279,3384,3490,3609,4192"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\b83b8b00b8346c9e7414a1f1298f055d\\transformed\\preference-1.2.1\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "105,181,270,350,502,671,752", "endColumns": "75,88,79,151,168,80,78", "endOffsets": "176,265,345,497,666,747,826"}, "to": {"startLines": "36,37,38,39,42,43,44", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "3614,3690,3779,3859,4197,4366,4447", "endColumns": "75,88,79,151,168,80,78", "endOffsets": "3685,3774,3854,4006,4361,4442,4521"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.12\\transforms\\0d0ad2c9a7eee0ad2b557032bddebd70\\transformed\\appcompat-1.1.0\\res\\values-my\\values-my.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1945,2060,2169,2268,2394,2501,2609,2769,2872", "endColumns": "112,106,115,86,108,122,81,81,90,91,94,93,100,92,94,93,90,90,83,114,108,98,125,106,107,159,102,84", "endOffsets": "213,320,436,523,632,755,837,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1940,2055,2164,2263,2389,2496,2604,2764,2867,2952"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,40", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,218,325,441,528,637,760,842,924,1015,1107,1202,1296,1397,1490,1585,1679,1770,1861,1945,2060,2169,2268,2394,2501,2609,2769,4011", "endColumns": "112,106,115,86,108,122,81,81,90,91,94,93,100,92,94,93,90,90,83,114,108,98,125,106,107,159,102,84", "endOffsets": "213,320,436,523,632,755,837,919,1010,1102,1197,1291,1392,1485,1580,1674,1765,1856,1940,2055,2164,2263,2389,2496,2604,2764,2867,4091"}}]}]}