class FormattingOption {
  final String id;
  final String displayText;
  final String type; // 'font', 'size', 'alignment', 'style'
  final dynamic value;
  final DateTime lastUsed;
  final int usageCount;

  FormattingOption({
    required this.id,
    required this.displayText,
    required this.type,
    required this.value,
    required this.lastUsed,
    this.usageCount = 1,
  });

  FormattingOption copyWith({
    String? id,
    String? displayText,
    String? type,
    dynamic value,
    DateTime? lastUsed,
    int? usageCount,
  }) {
    return FormattingOption(
      id: id ?? this.id,
      displayText: displayText ?? this.displayText,
      type: type ?? this.type,
      value: value ?? this.value,
      lastUsed: lastUsed ?? this.lastUsed,
      usageCount: usageCount ?? this.usageCount,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'displayText': displayText,
      'type': type,
      'value': value.toString(),
      'lastUsed': lastUsed.millisecondsSinceEpoch,
      'usageCount': usageCount,
    };
  }

  factory FormattingOption.fromJson(Map<String, dynamic> json) {
    return FormattingOption(
      id: json['id'],
      displayText: json['displayText'],
      type: json['type'],
      value: _parseValue(json['value'], json['type']),
      lastUsed: DateTime.fromMillisecondsSinceEpoch(json['lastUsed']),
      usageCount: json['usageCount'] ?? 1,
    );
  }

  static dynamic _parseValue(String valueStr, String type) {
    switch (type) {
      case 'size':
        return double.tryParse(valueStr) ?? 16.0;
      case 'alignment':
        return valueStr;
      case 'font':
        return valueStr;
      case 'style':
        return valueStr;
      default:
        return valueStr;
    }
  }
}
